# Freelance Marketplace Amplify Schema

enum UserRole {
  CLIENT
  FREELANCER
}

enum JobStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ProposalStatus {
  PENDING
  ACCEPTED
  REJECTED
}

enum ContractStatus {
  DRAFT
  ACTIVE
  COMPLETED
  CANCELLED
  DISPUTED
}

enum ContractType {
  FIXED_PRICE
  HOURLY
}

enum PaymentStatus {
  PAID
  PENDING
}

enum PaymentMethod {
  STRIPE
  USDC
  MYVILLAGETOKEN
}

type User @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  email: AWSEmail!
  role: UserRole!
  cognitoId: ID!
  profilePhoto: String
  bio: String
  skills: [String]

  jobs: [Job] @hasMany(fields: ["id"])
  proposals: [Proposal] @hasMany(fields: ["id"])
  clientContracts: [Contract] @hasMany(indexName: "byClient", fields: ["id"])
  freelancerContracts: [Contract] @hasMany(indexName: "byFreelancer", fields: ["id"])
  conversations: [Conversation] @hasMany(fields: ["id"])
  messages: [Message] @hasMany(fields: ["id"])
}

type Job @model @auth(rules: [{ allow: private }]) {
  id: ID!
  title: String!
  description: String!
  category: String!
  budget: Float!
  deadline: AWSDateTime
  isRemote: Boolean
  skills: [String]
  status: JobStatus @default(value: "OPEN")
  createdAt: AWSDateTime
  updatedAt: AWSDateTime

  clientId: ID!
  client: User @belongsTo(fields: ["clientId"])
  proposals: [Proposal] @hasMany(indexName: "byJob", fields: ["id"])
  contracts: [Contract] @hasMany(fields: ["id"])
  conversations: [Conversation] @hasMany(indexName: "byConversation", fields: ["id"])
}

type Proposal @model @auth(rules: [{ allow: private }]) {
  id: ID!
  bidAmount: Float!
  coverLetter: String
  status: ProposalStatus!
  proposedRate: Float
  createdAt: AWSDateTime
  updatedAt: AWSDateTime

  freelancerId: ID!
  freelancer: User @belongsTo(fields: ["freelancerId"])
  jobId: ID! @index(name: "byJob")
  job: Job @belongsTo(fields: ["jobId"])

  # Relationship to contract (one-to-one)
  contract: Contract @hasOne(fields: ["id"])
}

type Conversation @model @auth(rules: [{ allow: private }]) {
  id: ID!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  jobId: ID! @index(name: "byConversation")
  job: Job @belongsTo(fields: ["jobId"])

  clientId: ID! # reference to User
  client: User @belongsTo(fields: ["clientId"])

  freelancerId: ID! # reference to User
  freelancer: User @belongsTo(fields: ["freelancerId"])

  messagesData: [Message] @hasMany(indexName: "byMessages", fields: ["id"])
}

type Message @model @auth(rules: [{ allow: private }]) {
  id: ID!
  messageText: String!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  conversationId: ID! @index(name: "byMessages")
  conversationData: Conversation @belongsTo(fields: ["conversationId"])
  senderId: ID!
  sender: User @belongsTo(fields: ["senderId"])
}

type Contract @model @auth(rules: [{ allow: private }]) {
  id: ID!
  title: String!
  description: String
  type: ContractType!
  status: ContractStatus!
  terms: String!
  startDate: AWSDateTime!
  endDate: AWSDateTime
  budget: Float!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  jobId: ID! # reference to Job
  job: Job @belongsTo(fields: ["jobId"])

  proposalId: ID! # reference to Proposal
  proposal: Proposal @belongsTo(fields: ["proposalId"])

  clientId: ID! @index(name: "byClient") # reference to User
  client: User @belongsTo(fields: ["clientId"])

  freelancerId: ID! @index(name: "byFreelancer") # reference to User
  freelancer: User @belongsTo(fields: ["freelancerId"])
}

type Payment @model @auth(rules: [{ allow: private }]) {
  id: ID!
  amount: Float!
  status: PaymentStatus!
  method: PaymentMethod!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!

  contractId: ID! # reference to Contract
}
