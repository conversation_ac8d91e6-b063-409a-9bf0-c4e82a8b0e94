"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import contractService from "@/api/contracts/contract.service";
import {
  Contract,
  ContractStatus,
  ContractType,
  ExtendedContract,
  ContractJob,
  TimeEntry,
  ActivityLog,
  TabItem,
} from "@/types/features/contracts/contract.types";
import { UserProfile, UserRole } from "@/types/features/auth/auth.types";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";
import { Tabs } from "@/components/ui/Tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/Avatar";
import { useToast } from "@/components/ui/toast";
import {
  AlertCircle,
  Clock,
  Plus,
  RefreshCw,
  Check,
  CheckCircle,
  Download,
  MessageSquare,
  FileText,
  Calendar,
  DollarSign,
  User,
  Briefcase,
  Clock as ClockIcon,
  FileCheck,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

// Helper functions
const getStatusBadgeVariant = (status: ContractStatus) => {
  switch (status) {
    case ContractStatus.ACTIVE:
      return "success";
    case ContractStatus.DRAFT:
      return "warning";
    case ContractStatus.COMPLETED:
      return "outline";
    case ContractStatus.DISPUTED:
      return "destructive";
    case ContractStatus.CANCELLED:
      return "secondary";
    default:
      return "default";
  }
};

const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  try {
    return format(new Date(dateString), "MMM d, yyyy");
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
};

const formatCurrency = (amount?: number) => {
  if (amount === undefined) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const getContractTypeLabel = (type: ContractType) => {
  switch (type) {
    case ContractType.FIXED_PRICE:
      return "Fixed Price";
    case ContractType.HOURLY:
      return "Hourly";
    default:
      return type;
  }
};

const ContractPage = () => {
  const { id } = useParams() as { id?: string };
  const router = useRouter();
  const { showToast } = useToast();
  const {
    isAuthenticated,
    user,
    loading: authLoading,
  } = useAuth() as {
    isAuthenticated: boolean;
    user: UserProfile | null;
    loading: boolean;
  };

  const [contract, setContract] = useState<ExtendedContract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch contract data
  const fetchContract = useCallback(async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const data = await contractService.getContractById(id);
      setContract(data);
      setError(null);
    } catch (err) {
      console.error("Error fetching contract:", err);
      setError("Failed to load contract. Please try again.");
      showToast("Failed to load contract");
    } finally {
      setIsLoading(false);
    }
  }, [id, showToast]);

  // Handle contract status update
  const handleStatusUpdate = async (status: ContractStatus) => {
    if (!contract) return;

    try {
      setIsUpdating(true);
      await contractService.updateContractStatus(contract.id, status);
      await fetchContract();

      showToast(`Contract status updated to ${status}`);
    } catch (err) {
      console.error("Error updating contract status:", err);
      showToast("Failed to update contract status");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle contract download
  const handleDownloadContract = async () => {
    if (!contract) return;

    try {
      // TODO: Implement contract download logic
      showToast("Downloading contract...");
    } catch (err) {
      console.error("Error downloading contract:", err);
      showToast("Failed to download contract");
    }
  };

  // Handle send message
  const handleSendMessage = () => {
    if (!contract) return;

    // TODO: Implement send message logic
    const recipientId =
      user?.id === contract.client?.id
        ? contract.freelancer?.id
        : contract.client?.id;

    if (recipientId) {
      router.push(`/messages?userId=${recipientId}`);
    }
  };

  // Render status badge
  const renderStatusBadge = (status: ContractStatus) => (
    <Badge variant={getStatusBadgeVariant(status)} className="capitalize">
      {status.toLowerCase()}
    </Badge>
  );

  // Render action buttons based on user role and contract status
  const renderActionButtons = () => {
    if (!contract || !user) return null;

    const userIsClient = user.role === UserRole.CLIENT;
    const userIsFreelancer = user.role === UserRole.FREELANCER;
    const isActive = contract.status === ContractStatus.ACTIVE;
    const isDraft = contract.status === ContractStatus.DRAFT;

    return (
      <div className="flex flex-wrap gap-2">
        {(userIsClient || userIsFreelancer) && isActive && (
          <Button variant="outline" size="sm" onClick={handleSendMessage}>
            <MessageSquare className="mr-2 h-4 w-4" />
            Message
          </Button>
        )}

        {userIsClient && isDraft && (
          <Button
            size="sm"
            onClick={() => handleStatusUpdate(ContractStatus.ACTIVE)}
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Activate Contract
          </Button>
        )}

        <Button variant="outline" size="sm" onClick={handleDownloadContract}>
          <Download className="mr-2 h-4 w-4" />
          Download
        </Button>
      </div>
    );
  };

  // Render contract overview
  const renderContractOverview = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Status</p>
          {contract && renderStatusBadge(contract.status)}
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Type</p>
          <p>{contract ? getContractTypeLabel(contract.type) : "N/A"}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Start Date</p>
          <p>{contract ? formatDate(contract.startDate) : "N/A"}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">
            {contract?.type === ContractType.HOURLY
              ? "Hourly Rate"
              : "Contract Value"}
          </p>
          <p>
            {contract?.type === ContractType.HOURLY
              ? `${formatCurrency(contract.hourlyRate)}/hr`
              : formatCurrency(contract?.amount)}
          </p>
        </div>
      </div>

      {contract?.description && (
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Description</h3>
          <p className="text-muted-foreground">{contract.description}</p>
        </div>
      )}
    </div>
  );

  // Render contract details
  const renderContractDetails = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Scope of Work</h3>
        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="whitespace-pre-line">
            {contract?.scopeOfWork || "No scope of work provided."}
          </p>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium mb-2">Payment Terms</h3>
        <div className="bg-muted/50 p-4 rounded-lg">
          <p className="whitespace-pre-line">
            {contract?.paymentTerms || "No payment terms provided."}
          </p>
        </div>
      </div>

      {contract?.paymentSchedule && (
        <div>
          <h3 className="text-lg font-medium mb-2">Payment Schedule</h3>
          <div className="bg-muted/50 p-4 rounded-lg">
            <p className="whitespace-pre-line">{contract.paymentSchedule}</p>
          </div>
        </div>
      )}
    </div>
  );

  // Render activity log
  const renderActivityLog = () => (
    <div className="space-y-4">
      {contract?.activityLog && contract.activityLog.length > 0 ? (
        contract.activityLog.map((activity) => (
          <div key={activity.id} className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <Avatar className="h-8 w-8">
                <AvatarFallback>
                  {activity.userName
                    ? activity.userName.charAt(0).toUpperCase()
                    : "U"}
                </AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <p className="font-medium">
                  {activity.userName || "Unknown User"}
                </p>
                <span className="text-muted-foreground text-sm">
                  {formatDate(activity.timestamp)}
                </span>
              </div>
              <p className="text-muted-foreground">
                {activity.action}
                {activity.details && `: ${activity.details}`}
              </p>
            </div>
          </div>
        ))
      ) : (
        <p className="text-muted-foreground text-center py-8">
          No activity to display
        </p>
      )}
    </div>
  );

  // Render time tracking (for hourly contracts)
  const renderTimeTracking = () => (
    <div className="space-y-4">
      {contract?.timeEntries && contract.timeEntries.length > 0 ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Hours
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contract.hoursWorked || 0}
                </div>
                <p className="text-xs text-muted-foreground">All time</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">This Week</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {contract.hoursThisWeek || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Hours worked this week
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Earnings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(
                    (contract.hoursWorked || 0) * (contract.hourlyRate || 0)
                  )}
                </div>
                <p className="text-xs text-muted-foreground">Total earnings</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Time Entries</h3>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Time Entry
              </Button>
            </div>

            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Date
                    </th>
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Hours
                    </th>
                    <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                      Description
                    </th>
                    <th className="text-right p-3 text-sm font-medium text-muted-foreground">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {contract.timeEntries.map((entry) => (
                    <tr key={entry.id} className="border-b hover:bg-muted/50">
                      <td className="p-3">{formatDate(entry.date)}</td>
                      <td className="p-3">{entry.hours}</td>
                      <td className="p-3">
                        {entry.description || "No description"}
                      </td>
                      <td className="p-3 text-right">
                        <Badge
                          variant={
                            entry.status === "approved" ? "success" : "outline"
                          }
                          className="capitalize"
                        >
                          {entry.status}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <ClockIcon className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-2 text-sm font-medium">No time entries</h3>
          <p className="mt-1 text-sm text-muted-foreground">
            Get started by adding your first time entry.
          </p>
          <div className="mt-6">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Time Entry
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  // Set up tab items
  const tabItems: TabItem[] = [
    {
      id: "overview",
      label: "Overview",
      content: renderContractOverview(),
    },
    {
      id: "details",
      label: "Details",
      content: renderContractDetails(),
    },
    {
      id: "activity",
      label: "Activity Log",
      content: renderActivityLog(),
    },
  ];

  // Add time tracking tab for hourly contracts
  if (contract?.type === ContractType.HOURLY) {
    tabItems.splice(2, 0, {
      id: "time-tracking",
      label: "Time Tracking",
      content: renderTimeTracking(),
    });
  }

  // Initial data fetch
  useEffect(() => {
    fetchContract();
  }, [fetchContract]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <AlertCircle className="h-12 w-12 text-destructive" />
        <p className="text-lg font-medium">Failed to load contract</p>
        <p className="text-muted-foreground text-center max-w-md">
          {error || "The contract could not be loaded. Please try again later."}
        </p>
        <Button variant="outline" onClick={fetchContract} disabled={isLoading}>
          <RefreshCw
            className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
          />
          Retry
        </Button>
      </div>
    );
  }

  const isClient = user?.role === UserRole.CLIENT;
  const isFreelancer = user?.role === UserRole.FREELANCER;

  return (
    <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
      <ContentHeader
        title={contract.title || "Contract Details"}
        subtitle={`${
          contract.type === ContractType.HOURLY ? "Hourly" : "Fixed Price"
        } Contract • Created ${formatDate(contract.createdAt)}`}
        showBackButton={true}
        backButtonLabel="Back to Contracts"
        breadcrumbs={[
          {
            label: "Home",
            href: isClient ? "/client/dashboard" : "/freelancer/dashboard",
          },
          {
            label: "Contracts",
            href: "/contracts",
          },
          { label: contract.title || "Contract Details", current: true },
        ]}
      />

      <div className="w-full mx-auto py-4">
        <div className="bg-card rounded-lg shadow-sm border p-8">
          <div className="space-y-6">
            {/* Contract Status and Actions Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-end gap-4">
              <div className="flex flex-wrap gap-2 ">
                {renderActionButtons()}
              </div>
            </div>

            {/* Contract summary */}
            <Card>
              <CardHeader>
                <CardTitle>Contract Summary</CardTitle>
                <CardDescription>
                  {contract.type === ContractType.HOURLY
                    ? `Hourly contract at ${formatCurrency(
                        contract.hourlyRate
                      )}/hr`
                    : `Fixed price contract for ${formatCurrency(
                        contract.amount
                      )}`}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Start Date</p>
                    <p className="font-medium">
                      {formatDate(contract.startDate)}
                    </p>
                  </div>
                  {contract.endDate && (
                    <div>
                      <p className="text-sm text-muted-foreground">End Date</p>
                      <p className="font-medium">
                        {formatDate(contract.endDate)}
                      </p>
                    </div>
                  )}
                  {contract.type === ContractType.HOURLY &&
                    contract.hoursPerWeek && (
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Hours/Week
                        </p>
                        <p className="font-medium">{contract.hoursPerWeek}</p>
                      </div>
                    )}
                  <div>
                    <p className="text-sm text-muted-foreground">Total Value</p>
                    <p className="font-medium">
                      {contract.type === ContractType.HOURLY
                        ? `${formatCurrency(contract.hourlyRate)}/hr`
                        : formatCurrency(contract.amount)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tabs */}
            <Tabs
              items={tabItems}
              defaultTab={activeTab}
              onChange={setActiveTab}
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default ContractPage;
