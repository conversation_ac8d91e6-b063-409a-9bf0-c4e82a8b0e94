import {
  Contract,
  ContractFilters,
  ContractStatus,
  CreateContractDto,
  UpdateContractDto,
} from '@/types/features/contracts/contract.types';
import { contractApi } from './contract.api';

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const contractService = {
  async getContract(id: string): Promise<Contract> {
    try {
      return await contractApi.getContract(id);
    } catch (error) {
      return handleApiError('getContract', error);
    }
  },

  async listContracts(
    filters?: ContractFilters,
    limit: number = 10,
    nextToken?: string
  ): Promise<{ items: Contract[]; nextToken?: string }> {
    try {
      return await contractApi.listContracts(filters, limit, nextToken);
    } catch (error) {
      return handleApiError('listContracts', error);
    }
  },

  async getContractById(id: string): Promise<Contract> {
    try {
      return await contractApi.getContract(id);
    } catch (error) {
      return handleApiError('getContractById', error);
    }
  },

  async getContractsByJob(jobId: string): Promise<Contract[]> {
    try {
      return await contractApi.getContractsByJob(jobId);
    } catch (error) {
      return handleApiError('getContractsByJob', error);
    }
  },

  async getContractByProposal(proposalId: string): Promise<Contract | null> {
    try {
      const { items } = await contractApi.listContracts({ proposalId });
      return items.length > 0 ? items[0] : null;
    } catch (error) {
      console.error('Error checking contract by proposal:', error);
      return null;
    }
  },

  async hasExistingContract(proposalId: string): Promise<boolean> {
    try {
      const contract = await this.getContractByProposal(proposalId);
      return contract !== null;
    } catch (error) {
      console.error('Error checking existing contract:', error);
      return false;
    }
  },

  async getUserContracts(
    userId: string,
    status?: ContractStatus
  ): Promise<Contract[]> {
    try {
      return await contractApi.getUserContracts(userId, status);
    } catch (error) {
      return handleApiError('getUserContracts', error);
    }
  },

  async createContract(input: CreateContractDto): Promise<Contract> {
    try {
      return await contractApi.createContract(input);
    } catch (error) {
      return handleApiError('createContract', error);
    }
  },

  async updateContract(id: string, input: UpdateContractDto): Promise<Contract> {
    try {
      const updateData: UpdateContractDto & { id: string } = {
        ...input,
        id
      };
      return await contractApi.updateContract(updateData);
    } catch (error) {
      return handleApiError('updateContract', error);
    }
  },

  async updateContractStatus(id: string, status: ContractStatus): Promise<Contract> {
    try {
      return await contractApi.updateContractStatus(id, status);
    } catch (error) {
      return handleApiError('updateContractStatus', error);
    }
  },

  async acceptContract(id: string): Promise<Contract> {
    try {
      return await contractApi.acceptContract(id);
    } catch (error) {
      return handleApiError('acceptContract', error);
    }
  },

  async rejectContract(id: string): Promise<Contract> {
    try {
      return await contractApi.rejectContract(id);
    } catch (error) {
      return handleApiError('rejectContract', error);
    }
  },

  async completeContract(id: string): Promise<Contract> {
    try {
      return await contractApi.completeContract(id);
    } catch (error) {
      return handleApiError('completeContract', error);
    }
  },

};

export default contractService;
