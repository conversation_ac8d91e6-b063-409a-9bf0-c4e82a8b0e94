"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";

import { useAuth } from "@/lib/auth/AuthContext";
import { Button } from "@/components/ui/Button";
import { Loading } from "@/components/ui";
import { useToast } from "@/components/ui/toast";
import contractService from "@/api/contracts/contract.service";
import {
  Contract,
  ContractStatus,
  ContractType,
} from "@/types/features/contracts/contract.types";
import { Table } from "@/components/ui/Table";
import { Card, CardContent } from "@/components/ui/Card";
import { ContentHeader } from "@/components/layout/ContentHeader";
import { Badge } from "@/components/ui/Badge";
import { Icon } from "@/components/ui/Icon";

type TableData = Record<string, any>;

const ITEMS_PER_PAGE = 10;

const statusOptions = [
  { value: "all", label: "All Contracts" },
  { value: ContractStatus.ACTIVE, label: "Active" },
  { value: ContractStatus.DRAFT, label: "Pending" },
  { value: ContractStatus.COMPLETED, label: "Completed" },
  { value: ContractStatus.DISPUTED, label: "Disputed" },
  { value: ContractStatus.CANCELLED, label: "Cancelled" },
];

const getStatusBadgeVariant = (status: ContractStatus) => {
  switch (status) {
    case ContractStatus.ACTIVE:
      return "success";
    case ContractStatus.DRAFT:
      return "warning";
    case ContractStatus.COMPLETED:
      return "outline";
    case ContractStatus.DISPUTED:
      return "destructive";
    case ContractStatus.CANCELLED:
      return "secondary";
    default:
      return "default";
  }
};

const getContractTypeLabel = (type: ContractType) => {
  switch (type) {
    case ContractType.FIXED_PRICE:
      return "Fixed Price";
    case ContractType.HOURLY:
      return "Hourly";
    default:
      return type;
  }
};

export default function ContractsPage() {
  // Router and auth hooks
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    user,
    loading: authLoading,
    isAuthenticated,
    cognitoUserId,
  } = useAuth();
  const { showToast } = useToast();

  // State hooks
  const [contracts, setContracts] = useState<TableData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isFreelancer, setIsFreelancer] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [sortByValue, setSortByValue] = useState("createdAt_desc");
  const [sortBy, setSortBy] = useState<{
    field: string;
    direction: "asc" | "desc";
  }>({
    field: "createdAt",
    direction: "desc",
  });
  const [mounted, setMounted] = useState(false);

  const columns = [
    {
      header: "Contract",
      accessor: "title",
      cell: (value: unknown, contract: TableData) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
            <Icon name="FileText" className="h-5 w-5 text-primary" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-sm font-medium truncate">
              {contract.title ||
                contract.job?.title ||
                `Contract #${contract.id?.substring(0, 8) || ""}`}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {contract.job?.title
                ? contract.job.title
                : `ID: ${contract.id?.substring(0, 8) || "N/A"}`}
            </p>
          </div>
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("title"),
      sortDirection: sortBy.field === "title" ? sortBy.direction : undefined,
    },
    {
      header: "Type",
      accessor: "type",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {contract.type ? getContractTypeLabel(contract.type) : "N/A"}
          </span>
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("type"),
      sortDirection: sortBy.field === "type" ? sortBy.direction : undefined,
    },
    {
      header: "Amount",
      accessor: "budget",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm font-medium">
          {contract.budget
            ? `$${Number(contract.budget).toLocaleString()}`
            : "TBD"}
          <span className="text-xs text-muted-foreground block">
            {contract.type === "HOURLY"
              ? "per hour"
              : contract.type === "FIXED_PRICE"
              ? "fixed price"
              : "amount"}
          </span>
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("budget"),
      sortDirection: sortBy.field === "budget" ? sortBy.direction : undefined,
    },
    {
      header: "Status",
      accessor: "status",
      cell: (value: unknown, contract: TableData) => (
        <Badge
          variant={getStatusBadgeVariant(contract.status)}
          className="capitalize"
        >
          {contract.status.toLowerCase()}
        </Badge>
      ),
      sortable: true,
      onSort: () => handleSort("status"),
      sortDirection: sortBy.field === "status" ? sortBy.direction : undefined,
    },
    {
      header: "Created",
      accessor: "createdAt",
      cell: (value: unknown, contract: TableData) => (
        <div className="text-sm text-muted-foreground">
          {formatDistanceToNow(new Date(contract.createdAt), {
            addSuffix: true,
          })}
        </div>
      ),
      sortable: true,
      onSort: () => handleSort("createdAt"),
      sortDirection:
        sortBy.field === "createdAt" ? sortBy.direction : undefined,
    },
    {
      header: "Actions",
      accessor: "id",
      cell: (value: unknown, contract: TableData) => {
        const onView = (e: React.MouseEvent) => {
          e.stopPropagation();
          router.push(`/contracts/${contract.id}`);
        };

        return (
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={onView}
              className="h-8 px-2 py-1"
              title="View contract details"
            >
              <Icon name="Eye" className="h-4 w-4 mr-1" />
            </Button>
          </div>
        );
      },
      className: "text-right",
    },
  ];

  const fetchContracts = useCallback(async () => {
    if (!user || !cognitoUserId) return;

    setIsLoading(true);
    setError(null);

    try {
      const page = searchParams.get("page")
        ? parseInt(searchParams.get("page") as string)
        : 1;
      const status = statusFilter === "all" ? undefined : statusFilter;

      const filters: any = {
        status,
        limit: ITEMS_PER_PAGE,
        offset: (page - 1) * ITEMS_PER_PAGE,
        sortBy: sortBy.field,
        sortOrder: sortBy.direction.toUpperCase(),
      };

      if (isClient) {
        filters.clientId = cognitoUserId;
      } else if (isFreelancer) {
        filters.freelancerId = cognitoUserId;
      }

      const response = await contractService.listContracts(
        filters,
        filters.limit,
        undefined
      );
      setContracts((response.items || []) as TableData[]);
      const currentItemCount = response.items?.length || 0;
      const hasMorePages = !!response.nextToken;

      // For pagination display: if we have items equal to ITEMS_PER_PAGE, assume there might be more
      if (currentItemCount === ITEMS_PER_PAGE && hasMorePages) {
        // Show pagination controls by setting estimated total
        setTotalItems(page * ITEMS_PER_PAGE + 1); // Show at least one more page
      } else {
        // Last page or fewer items than limit
        setTotalItems((page - 1) * ITEMS_PER_PAGE + currentItemCount);
      }

      setCurrentPage(page);
      setHasMore(hasMorePages);
    } catch (err) {
      console.error("Error fetching contracts:", err);
      setError("Failed to load contracts. Please try again.");
      showToast("Failed to load contracts. Please try again.");
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [
    user,
    cognitoUserId,
    statusFilter,
    sortBy,
    searchParams,
    isClient,
    isFreelancer,
    showToast,
  ]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchContracts();
  };

  useEffect(() => {
    if (user) {
      const userRole = user.attributes?.["custom:role"];
      setIsClient(userRole === "CLIENT");
      setIsFreelancer(userRole === "FREELANCER");
    }
  }, [user]);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (user) {
      fetchContracts();
    }
  }, [user, statusFilter, sortBy, searchParams, fetchContracts]);

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("page", page.toString());
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleSort = (field: string) => {
    setSortBy((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    // Reset to first page when changing filters
    const params = new URLSearchParams(searchParams);
    params.set("page", "1");
    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Handler functions that use hooks must be declared before any early returns
  const handleSortChange = useCallback((value: string) => {
    setSortByValue(value);
    const [field, direction] = value.split("_");
    setSortBy({
      field,
      direction: direction as "asc" | "desc",
    });
  }, []);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [authLoading, isAuthenticated, router]);

  if (!mounted || authLoading || !isAuthenticated || !cognitoUserId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-10 w-64 bg-muted/30 rounded-lg animate-pulse mb-4" />
        <div className="h-10 bg-muted/30 rounded-lg animate-pulse mb-4" />
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted/30 rounded-lg animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-4 sm:p-6 space-y-6">
      <div className="flex justify-between items-center mb-6">
        <ContentHeader
          title="My Contracts"
          subtitle="Manage your contracts and agreements"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Contracts", current: true },
          ]}
        />
        <div className="flex items-center space-x-3">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Icon name="Filter" size="sm" />
            <span>Filters</span>
          </Button>
        </div>
      </div>

      <div className="space-y-4">
        {showFilters && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Status
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                  >
                    {statusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Type
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={statusFilter} // placeholder for type filter state
                    onChange={(e) => {}}
                  >
                    <option value="all">All Types</option>
                    <option value="FIXED_PRICE">Fixed Price</option>
                    <option value="HOURLY">Hourly</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-muted-foreground mb-1">
                    Sort By
                  </label>
                  <select
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    value={sortByValue}
                    onChange={(e) => handleSortChange(e.target.value)}
                  >
                    <option value="createdAt_desc">Newest</option>
                    <option value="createdAt_asc">Oldest</option>
                    <option value="title_asc">Title (A-Z)</option>
                    <option value="title_desc">Title (Z-A)</option>
                    <option value="budget_asc">Budget (Low to High)</option>
                    <option value="budget_desc">Budget (High to Low)</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex justify-end space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    // Reset filters
                    setStatusFilter("all");
                    setSortByValue("createdAt_desc");
                    setSortBy({ field: "createdAt", direction: "desc" });
                  }}
                >
                  Reset
                </Button>
                <Button
                  size="sm"
                  onClick={() => {
                    fetchContracts();
                    setShowFilters(false);
                  }}
                >
                  Apply Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Card>
        <CardContent className="p-0">
          {error ? (
            <div className="p-6 text-center">
              <p className="text-destructive mb-4">{error}</p>
              <Button
                variant="outline"
                onClick={fetchContracts}
                disabled={isRefreshing}
              >
                {isRefreshing ? "Retrying..." : "Retry"}
              </Button>
            </div>
          ) : contracts.length > 0 ? (
            <Table
              data={contracts}
              columns={columns}
              emptyState={
                <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
                  <div className="rounded-full bg-primary/10 p-4 mb-4">
                    <Icon name="FileText" className="h-10 w-10 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    No contracts found
                  </h3>
                  <p className="text-muted-foreground max-w-md">
                    {statusFilter === "all"
                      ? "You don't have any contracts yet. Get started by creating a new contract."
                      : `No ${statusFilter.toLowerCase()} contracts found. Try adjusting your filters.`}
                  </p>
                  <div className="mt-6 space-x-3">
                    <Button onClick={() => router.push("/contracts/new")}>
                      <Icon name="Plus" className="mr-2 h-4 w-4" />
                      Create Contract
                    </Button>
                    {statusFilter !== "all" && (
                      <Button
                        variant="outline"
                        onClick={() => setStatusFilter("all")}
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                </div>
              }
              loading={isLoading}
              pagination={{
                currentPage,
                totalPages: Math.max(1, Math.ceil(totalItems / ITEMS_PER_PAGE)),
                onPageChange: handlePageChange,
                hasNextPage: hasMore,
                showPagination: totalItems > ITEMS_PER_PAGE || currentPage > 1,
              }}
            />
          ) : (
            <div className="p-8 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-muted">
                <Icon
                  name="FileText"
                  className="h-6 w-6 text-muted-foreground"
                />
              </div>
              <h3 className="mt-4 text-sm font-medium">No contracts found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {statusFilter === "all"
                  ? "Get started by creating a new contract."
                  : `No ${statusOptions
                      .find((opt) => opt.value === statusFilter)
                      ?.label.toLowerCase()} contracts found.`}
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/contracts/new">
                    <Icon name="Plus" className="mr-2 h-4 w-4" />
                    New Contract
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
